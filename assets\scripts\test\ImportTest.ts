/**
 * 导入测试脚本
 * 验证所有模块都能正确导入
 */

// 测试管理器导入
import { DataManager } from '../managers/DataManager.ts';
import { UIManager } from '../managers/UIManager.ts';
import { SceneManager } from '../managers/SceneManager.ts';
import { EventManager } from '../managers/EventManager.ts';

// 测试场景导入
import { MainScene } from '../scenes/MainScene.ts';
import { BaseScene } from '../scenes/BaseScene.ts';
import { HouseScene } from '../scenes/HouseScene.ts';
import { BackyardScene } from '../scenes/BackyardScene.ts';

// 测试UI组件导入
import { MessageBox } from '../ui/components/MessageBox.ts';
import { LoadingPanel } from '../ui/components/LoadingPanel.ts';
import { ToastMessage } from '../ui/components/ToastMessage.ts';
import { DrawerComponent } from '../ui/components/DrawerComponent.ts';
import { ItemDisplay } from '../ui/components/ItemDisplay.ts';
import { AnimalDisplay } from '../ui/components/AnimalDisplay.ts';

// 测试数据结构导入
import { AnimalInstance, AnimalStatus, InteractionType } from '../data/AnimalInstance.ts';
import { ItemData, ItemType, ItemRarity } from '../data/ItemData.ts';

// 测试配置导入
import { GameConfig } from '../config/GameConfig.ts';

// 测试主管理器导入
import { GameManager } from '../GameManager.ts';

console.log("=== 模块导入测试 ===");

// 测试管理器类
console.log("管理器类导入测试:");
console.log("- DataManager:", typeof DataManager);
console.log("- UIManager:", typeof UIManager);
console.log("- SceneManager:", typeof SceneManager);
console.log("- EventManager:", typeof EventManager);
console.log("- GameManager:", typeof GameManager);

// 测试场景类
console.log("\n场景类导入测试:");
console.log("- MainScene:", typeof MainScene);
console.log("- BaseScene:", typeof BaseScene);
console.log("- HouseScene:", typeof HouseScene);
console.log("- BackyardScene:", typeof BackyardScene);

// 测试UI组件类
console.log("\nUI组件类导入测试:");
console.log("- MessageBox:", typeof MessageBox);
console.log("- LoadingPanel:", typeof LoadingPanel);
console.log("- ToastMessage:", typeof ToastMessage);
console.log("- DrawerComponent:", typeof DrawerComponent);
console.log("- ItemDisplay:", typeof ItemDisplay);
console.log("- AnimalDisplay:", typeof AnimalDisplay);

// 测试枚举
console.log("\n枚举导入测试:");
console.log("- AnimalStatus:", typeof AnimalStatus);
console.log("- InteractionType:", typeof InteractionType);
console.log("- ItemType:", typeof ItemType);
console.log("- ItemRarity:", typeof ItemRarity);

// 测试配置
console.log("\n配置导入测试:");
console.log("- GameConfig:", typeof GameConfig);
console.log("- GameConfig.VERSION:", GameConfig.VERSION);

// 测试接口（通过创建对象）
console.log("\n接口测试:");

// 测试AnimalInstance接口
const testAnimalInstance: AnimalInstance = {
    instanceId: "test-001",
    animalId: "cat_001",
    level: 1,
    experience: 0,
    happiness: 80,
    health: 90,
    cleanliness: 70,
    hunger: 60,
    friendship: 50,
    adoptedTime: Date.now(),
    lastInteractTime: Date.now(),
    totalInteractions: 0,
    status: AnimalStatus.HOME,
    traits: [],
    memories: []
};
console.log("- AnimalInstance接口测试: ✓");

// 测试ItemData接口
const testItemData: ItemData = {
    id: "test_item",
    name: "测试道具",
    type: ItemType.FOOD,
    rarity: ItemRarity.COMMON,
    description: "这是一个测试道具",
    iconPath: "test/icon",
    price: 10,
    effect: "测试效果"
};
console.log("- ItemData接口测试: ✓");

console.log("\n=== 所有模块导入成功! ===");

/**
 * 导出测试函数供其他模块调用
 */
export function runImportTest(): boolean {
    try {
        // 简单的类型检查
        const checks = [
            typeof DataManager === 'function',
            typeof UIManager === 'function',
            typeof SceneManager === 'function',
            typeof EventManager === 'function',
            typeof GameManager === 'function',
            typeof MainScene === 'function',
            typeof BaseScene === 'function',
            typeof HouseScene === 'function',
            typeof BackyardScene === 'function',
            typeof MessageBox === 'function',
            typeof LoadingPanel === 'function',
            typeof ToastMessage === 'function',
            typeof DrawerComponent === 'function',
            typeof ItemDisplay === 'function',
            typeof AnimalDisplay === 'function',
            typeof GameConfig === 'function'
        ];
        
        const allPassed = checks.every(check => check);
        
        if (allPassed) {
            console.log("✅ 导入测试通过");
            return true;
        } else {
            console.log("❌ 导入测试失败");
            return false;
        }
    } catch (error) {
        console.error("❌ 导入测试出错:", error);
        return false;
    }
}

/**
 * 测试枚举值
 */
export function testEnumValues(): boolean {
    try {
        // 测试AnimalStatus枚举
        const animalStatuses = [
            AnimalStatus.HOME,
            AnimalStatus.TRAVELING,
            AnimalStatus.VISITING,
            AnimalStatus.SLEEPING,
            AnimalStatus.PLAYING,
            AnimalStatus.EATING,
            AnimalStatus.SICK
        ];
        
        // 测试InteractionType枚举
        const interactionTypes = [
            InteractionType.FEED,
            InteractionType.PLAY,
            InteractionType.PET,
            InteractionType.CLEAN,
            InteractionType.TALK,
            InteractionType.PHOTO,
            InteractionType.GIFT,
            InteractionType.TRAIN
        ];
        
        // 测试ItemType枚举
        const itemTypes = [
            ItemType.FOOD,
            ItemType.TOY,
            ItemType.TOOL,
            ItemType.SUMMON_SCROLL,
            ItemType.DECORATION
        ];
        
        // 测试ItemRarity枚举
        const itemRarities = [
            ItemRarity.COMMON,
            ItemRarity.UNCOMMON,
            ItemRarity.RARE,
            ItemRarity.EPIC,
            ItemRarity.LEGENDARY
        ];
        
        console.log("枚举值测试:");
        console.log("- AnimalStatus枚举值数量:", animalStatuses.length);
        console.log("- InteractionType枚举值数量:", interactionTypes.length);
        console.log("- ItemType枚举值数量:", itemTypes.length);
        console.log("- ItemRarity枚举值数量:", itemRarities.length);
        
        return true;
    } catch (error) {
        console.error("❌ 枚举值测试出错:", error);
        return false;
    }
}

/**
 * 运行完整的导入和类型测试
 */
export function runFullImportTest(): boolean {
    console.log("=== 开始完整导入测试 ===");
    
    const importTest = runImportTest();
    const enumTest = testEnumValues();
    
    const allPassed = importTest && enumTest;
    
    if (allPassed) {
        console.log("🎉 所有导入测试通过!");
    } else {
        console.log("💥 部分导入测试失败!");
    }
    
    console.log("=== 导入测试完成 ===");
    
    return allPassed;
}
