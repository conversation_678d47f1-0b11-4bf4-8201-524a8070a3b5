/* 动物收容所游戏按钮样式参考 */

/* 主屋按钮 - 棕色主题 */
.house-button {
    background: linear-gradient(135deg, #8D6E63 0%, #6D4C41 100%);
    border: 3px solid #5D4037;
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    color: white;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.house-button:hover {
    background: linear-gradient(135deg, #A1887F 0%, #8D6E63 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.4);
}

/* 后院按钮 - 绿色主题 */
.backyard-button {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    border: 3px solid #2E7D32;
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    color: white;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.backyard-button:hover {
    background: linear-gradient(135deg, #66BB6A 0%, #4CAF50 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.4);
}

/* 商店按钮 - 橙色主题 */
.shop-button {
    background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
    border: 3px solid #E65100;
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    color: white;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.shop-button:hover {
    background: linear-gradient(135deg, #FFB74D 0%, #FF9800 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.4);
}

/* 微信按钮 - 绿色主题 */
.wechat-button {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    border: 3px solid #2E7D32;
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    color: white;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* 设置按钮 - 灰蓝色主题 */
.settings-button {
    background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
    border: 3px solid #37474F;
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    color: white;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* 成就按钮 - 金色主题 */
.achievement-button {
    background: linear-gradient(135deg, #FFC107 0%, #FF8F00 100%);
    border: 3px solid #FF6F00;
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    color: white;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* 通用按钮样式 */
.game-button {
    width: 150px;
    height: 80px;
    font-size: 18px;
    font-family: "Microsoft YaHei", sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.game-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.game-button:hover::before {
    left: 100%;
}

/* 状态栏样式 */
.status-bar {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #E0E0E0;
    border-radius: 25px;
    padding: 10px 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.coin-display, .gem-display {
    display: inline-flex;
    align-items: center;
    margin: 0 10px;
    font-weight: bold;
    font-size: 16px;
}

.coin-icon {
    width: 24px;
    height: 24px;
    background: radial-gradient(circle, #FFD700 0%, #FFA000 100%);
    border-radius: 50%;
    margin-right: 8px;
    border: 2px solid #FF8F00;
}

.gem-icon {
    width: 24px;
    height: 24px;
    background: linear-gradient(45deg, #2196F3 0%, #1976D2 100%);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    margin-right: 8px;
}

/* 动物来访通知样式 */
.animal-visit-notification {
    background: linear-gradient(135deg, #FFEB3B 0%, #FFC107 100%);
    border: 3px solid #FF8F00;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* 背景样式 */
.main-scene-background {
    background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);
    position: relative;
}

.main-scene-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120, 200, 120, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(100, 180, 100, 0.3) 0%, transparent 50%);
}

/* 标题样式 */
.main-title {
    font-size: 36px;
    font-weight: bold;
    color: #2E7D32;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    text-align: center;
    margin: 20px 0;
}

/* 欢迎文本样式 */
.welcome-text {
    font-size: 18px;
    color: #4CAF50;
    text-align: center;
    margin: 10px 0;
    font-style: italic;
}
