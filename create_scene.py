#!/usr/bin/env python3
"""
创建完整的Cocos Creator主场景文件
"""

import json
import uuid

def generate_uuid():
    """生成UUID"""
    return str(uuid.uuid4())

def create_main_scene():
    """创建主场景数据"""
    
    # 生成所有需要的UUID
    scene_uuid = "04944abe-68c4-47b0-8898-1dd04480bc1e"
    canvas_uuid = generate_uuid()
    bg_uuid = generate_uuid()
    title_uuid = generate_uuid()
    camera_uuid = generate_uuid()
    button_container_uuid = generate_uuid()
    
    # 按钮UUIDs
    house_btn_uuid = generate_uuid()
    backyard_btn_uuid = generate_uuid()
    shop_btn_uuid = generate_uuid()
    wechat_btn_uuid = generate_uuid()
    settings_btn_uuid = generate_uuid()
    achievement_btn_uuid = generate_uuid()
    
    scene_data = [
        {
            "__type__": "cc.SceneAsset",
            "_name": "MainScene",
            "_objFlags": 0,
            "__editorExtras__": {},
            "_native": "",
            "scene": {"__id__": 1}
        },
        {
            "__type__": "cc.Scene",
            "_name": "MainScene",
            "_objFlags": 0,
            "__editorExtras__": {},
            "_parent": None,
            "_children": [{"__id__": 2}],
            "_active": True,
            "_components": [],
            "_prefab": None,
            "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0},
            "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1},
            "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1},
            "_mobility": 0,
            "_layer": 1073741824,
            "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0},
            "autoReleaseAssets": False,
            "_globals": {"__id__": 20},
            "_id": scene_uuid
        },
        {
            "__type__": "cc.Node",
            "_name": "Canvas",
            "_objFlags": 0,
            "__editorExtras__": {},
            "_parent": {"__id__": 1},
            "_children": [
                {"__id__": 3},  # Background
                {"__id__": 6},  # Title
                {"__id__": 9},  # ButtonContainer
                {"__id__": 16}, # Camera
            ],
            "_active": True,
            "_components": [
                {"__id__": 17}, # Canvas
                {"__id__": 18}, # UITransform
                {"__id__": 19}, # MainScene Script
            ],
            "_prefab": None,
            "_lpos": {"__type__": "cc.Vec3", "x": 360, "y": 540, "z": 0},
            "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1},
            "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1},
            "_mobility": 0,
            "_layer": 33554432,
            "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0},
            "_id": canvas_uuid
        },
        # Background Node
        {
            "__type__": "cc.Node",
            "_name": "Background",
            "_objFlags": 0,
            "__editorExtras__": {},
            "_parent": {"__id__": 2},
            "_children": [],
            "_active": True,
            "_components": [
                {"__id__": 4}, # UITransform
                {"__id__": 5}, # Sprite
            ],
            "_prefab": None,
            "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0},
            "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1},
            "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1},
            "_mobility": 0,
            "_layer": 33554432,
            "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0},
            "_id": bg_uuid
        },
        # Background UITransform
        {
            "__type__": "cc.UITransform",
            "_name": "",
            "_objFlags": 0,
            "__editorExtras__": {},
            "node": {"__id__": 3},
            "_enabled": True,
            "__prefab": None,
            "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1080},
            "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5},
            "_id": generate_uuid()
        },
        # Background Sprite
        {
            "__type__": "cc.Sprite",
            "_name": "",
            "_objFlags": 0,
            "__editorExtras__": {},
            "node": {"__id__": 3},
            "_enabled": True,
            "__prefab": None,
            "_customMaterial": None,
            "_srcBlendFactor": 2,
            "_dstBlendFactor": 4,
            "_color": {"__type__": "cc.Color", "r": 232, "g": 245, "b": 232, "a": 255},
            "_spriteFrame": {
                "__uuid__": "57520716-48c8-4a19-8acf-41c9f8777fb0@f9941",
                "__expectedType__": "cc.SpriteFrame"
            },
            "_type": 0,
            "_fillType": 0,
            "_sizeMode": 0,
            "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0},
            "_fillStart": 0,
            "_fillRange": 0,
            "_isTrimmedMode": True,
            "_useGrayscale": False,
            "_atlas": None,
            "_id": generate_uuid()
        }
    ]
    
    return scene_data

def main():
    """主函数"""
    print("正在创建主场景文件...")
    
    scene_data = create_main_scene()
    
    # 保存场景文件
    with open("assets/MainScene_New.scene", "w", encoding="utf-8") as f:
        json.dump(scene_data, f, indent=2, ensure_ascii=False)
    
    print("主场景文件已创建: assets/MainScene_New.scene")

if __name__ == "__main__":
    main()
