# 主场景设置指南

## 场景结构

在Cocos Creator中创建以下节点结构：

```
MainScene (Scene)
└── Canvas (Canvas)
    ├── Background (Sprite)
    ├── Header (Node)
    │   ├── Title (Label) - "我的动物收容所"
    │   └── StatusBar (Node)
    │       ├── CoinsContainer (Node)
    │       │   ├── CoinIcon (Sprite)
    │       │   └── CoinsLabel (Label)
    │       └── GemsContainer (Node)
    │           ├── GemIcon (Sprite)
    │           └── GemsLabel (Label)
    ├── MainContent (Node)
    │   ├── WelcomeText (Label) - "欢迎来到动物收容所"
    │   ├── PlayerNameLabel (Label) - "玩家名称"
    │   └── AnimalVisitNotification (Node)
    │       ├── NotificationBg (Sprite)
    │       ├── NotificationText (Label) - "有动物来访！"
    │       └── NotificationButton (Button)
    └── ButtonContainer (Node)
        ├── HouseButton (Button)
        │   ├── ButtonBg (Sprite)
        │   ├── ButtonIcon (Sprite)
        │   └── ButtonLabel (Label) - "主屋"
        ├── BackyardButton (Button)
        │   ├── ButtonBg (Sprite)
        │   ├── ButtonIcon (Sprite)
        │   └── ButtonLabel (Label) - "后院"
        ├── ShopButton (Button)
        │   ├── ButtonBg (Sprite)
        │   ├── ButtonIcon (Sprite)
        │   └── ButtonLabel (Label) - "商店"
        └── WechatButton (Button)
            ├── ButtonBg (Sprite)
            ├── ButtonIcon (Sprite)
            └── ButtonLabel (Label) - "微信"
```

## 组件绑定

1. 选中Canvas节点
2. 添加MainScene脚本组件
3. 将对应的节点拖拽到脚本属性中：
   - houseButton → HouseButton
   - backyardButton → BackyardButton
   - shopButton → ShopButton
   - wechatButton → WechatButton
   - playerNameLabel → PlayerNameLabel
   - coinsLabel → CoinsLabel
   - gemsLabel → GemsLabel
   - animalVisitNotification → AnimalVisitNotification

## 样式设置

### 背景
- 颜色：浅绿色 (#E8F5E8) 或使用背景图片
- 大小：铺满整个Canvas

### 标题
- 字体大小：48
- 颜色：深绿色 (#2E7D32)
- 位置：顶部居中

### 状态栏
- 位置：右上角
- 金币图标：金色圆形
- 宝石图标：蓝色菱形

### 按钮样式
- 大小：150x80
- 圆角：10px
- 主屋按钮：棕色 (#8D6E63)
- 后院按钮：绿色 (#4CAF50)
- 商店按钮：橙色 (#FF9800)
- 微信按钮：绿色 (#4CAF50)

### 动物来访通知
- 背景：半透明黄色
- 位置：屏幕中央偏上
- 默认隐藏，有动物来访时显示

## 布局建议

- 使用Widget组件进行自适应布局
- 按钮采用网格布局，2x2排列
- 状态栏固定在顶部
- 通知弹窗居中显示
