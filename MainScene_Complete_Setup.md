# 主场景完整设置教程

## 🎯 完成的功能

我已经为你完善了MainScene.ts，新增了以下功能：

### ✨ 新增功能
1. **智能欢迎文本** - 根据时间显示不同的问候语
2. **数字格式化** - 大数字显示为K/M格式
3. **动物来访系统** - 随机动物来访并给予奖励
4. **入场动画** - 按钮入场动画效果
5. **定时器系统** - 自动更新UI和检查动物来访
6. **设置和成就按钮** - 预留功能入口
7. **首次登录检测** - 新玩家特殊欢迎

### 🎨 生成的资源
- **按钮图标** - 6个主要按钮的图标
- **UI图标** - 金币、宝石等UI元素图标
- **样式参考** - CSS样式文件作为设计参考

## 🛠️ 在Cocos Creator中设置

### 1. 导入资源
1. 将 `button_icons` 文件夹中的所有图片导入到项目的 `assets/textures/ui/` 目录
2. 设置图片类型为 `Sprite Frame`

### 2. 创建场景结构

```
MainScene (Scene)
└── Canvas (Canvas)
    ├── Background (Sprite) 
    │   └── 设置颜色为 #E8F5E8 或使用背景图
    ├── Header (Node)
    │   ├── Title (Label) 
    │   │   └── 文本: "我的动物收容所"
    │   │   └── 字体大小: 48, 颜色: #2E7D32
    │   └── StatusBar (Node)
    │       ├── CoinsContainer (Node)
    │       │   ├── CoinIcon (Sprite) → 使用 coin_icon.png
    │       │   └── CoinsLabel (Label)
    │       └── GemsContainer (Node)
    │           ├── GemIcon (Sprite) → 使用 gem_icon.png
    │           └── GemsLabel (Label)
    ├── MainContent (Node)
    │   ├── WelcomeLabel (Label)
    │   │   └── 文本: "欢迎来到我的动物收容所！"
    │   ├── PlayerNameLabel (Label)
    │   └── AnimalVisitNotification (Node)
    │       ├── NotificationBg (Sprite)
    │       │   └── 颜色: #FFEB3B, 圆角矩形
    │       ├── NotificationText (Label)
    │       │   └── 文本: "有动物来访！"
    │       └── NotificationButton (Button)
    │           └── 点击事件: MainScene.onAnimalVisitClick
    └── ButtonContainer (Node)
        ├── HouseButton (Button)
        │   ├── Background (Sprite) → 使用 主屋_button.png
        │   ├── Icon (Sprite) → 使用 house_icon.png
        │   └── Label (Label) → "主屋"
        ├── BackyardButton (Button)
        │   ├── Background (Sprite) → 使用 后院_button.png
        │   ├── Icon (Sprite) → 使用 backyard_icon.png
        │   └── Label (Label) → "后院"
        ├── ShopButton (Button)
        │   ├── Background (Sprite) → 使用 商店_button.png
        │   ├── Icon (Sprite) → 使用 shop_icon.png
        │   └── Label (Label) → "商店"
        ├── WechatButton (Button)
        │   ├── Background (Sprite) → 使用 微信_button.png
        │   ├── Icon (Sprite) → 使用 wechat_icon.png
        │   └── Label (Label) → "微信"
        ├── SettingsButton (Button)
        │   ├── Background (Sprite) → 使用 设置_button.png
        │   ├── Icon (Sprite) → 使用 settings_icon.png
        │   └── Label (Label) → "设置"
        └── AchievementButton (Button)
            ├── Background (Sprite) → 使用 成就_button.png
            ├── Icon (Sprite) → 使用 achievement_icon.png
            └── Label (Label) → "成就"
```

### 3. 绑定脚本组件

1. **选中Canvas节点**
2. **添加MainScene脚本组件**
3. **拖拽节点到对应属性**：
   - House Button → HouseButton
   - Backyard Button → BackyardButton
   - Shop Button → ShopButton
   - Wechat Button → WechatButton
   - Settings Button → SettingsButton
   - Achievement Button → AchievementButton
   - Player Name Label → PlayerNameLabel
   - Coins Label → CoinsLabel
   - Gems Label → GemsLabel
   - Welcome Label → WelcomeLabel
   - Animal Visit Notification → AnimalVisitNotification
   - Status Bar → StatusBar
   - Button Container → ButtonContainer

### 4. 布局设置

#### 按钮布局 (ButtonContainer)
- 使用 **Layout** 组件
- Type: **GRID**
- Start Axis: **HORIZONTAL**
- Cell Size: **150x80**
- Spacing: **20x20**
- Padding: **20**

#### 状态栏布局 (StatusBar)
- 使用 **Layout** 组件
- Type: **HORIZONTAL**
- Spacing: **20**
- 使用 **Widget** 组件固定到右上角

### 5. 动画设置（可选）

为按钮添加点击动画：
1. 选中按钮节点
2. 添加 **Button** 组件的 **Transition** 设置
3. 设置 **Scale Transition**：
   - Normal Scale: 1.0
   - Pressed Scale: 0.95
   - Duration: 0.1

## 🎮 测试功能

启动游戏后你应该能看到：

1. ✅ **时间相关的欢迎文本**
2. ✅ **金币和宝石显示**（格式化数字）
3. ✅ **6个功能按钮**
4. ✅ **随机动物来访通知**（每5秒检查一次）
5. ✅ **点击动物通知获得奖励**
6. ✅ **每日登录奖励**

## 🔧 下一步

1. **创建其他场景** (HouseScene, BackyardScene等)
2. **完善UI管理器** 的消息显示功能
3. **添加音效和背景音乐**
4. **实现数据持久化**

现在你的主场景应该完全可以正常运行了！🎉
