#!/usr/bin/env python3
"""
按钮图标生成脚本
为动物收容所游戏生成简单的按钮图标
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_button_icon(text, bg_color, text_color, size=(128, 128), filename=None):
    """创建按钮图标"""
    # 创建图像
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制圆角矩形背景
    margin = 10
    draw.rounded_rectangle(
        [margin, margin, size[0]-margin, size[1]-margin],
        radius=15,
        fill=bg_color,
        outline=(255, 255, 255, 100),
        width=2
    )
    
    # 绘制文字
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        # 如果没有找到字体，使用默认字体
        font = ImageFont.load_default()
    
    # 计算文字位置（居中）
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    # 绘制文字阴影
    draw.text((x+2, y+2), text, font=font, fill=(0, 0, 0, 100))
    # 绘制文字
    draw.text((x, y), text, font=font, fill=text_color)
    
    # 保存图像
    if filename:
        img.save(filename, 'PNG')
        print(f"已生成: {filename}")
    
    return img

def create_icon_with_emoji(emoji, bg_color, size=(128, 128), filename=None):
    """创建带emoji的图标"""
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制圆形背景
    margin = 10
    draw.ellipse(
        [margin, margin, size[0]-margin, size[1]-margin],
        fill=bg_color,
        outline=(255, 255, 255, 150),
        width=3
    )
    
    # 绘制emoji（作为文字）
    try:
        font = ImageFont.truetype("seguiemj.ttf", 48)  # Windows emoji字体
    except:
        try:
            font = ImageFont.truetype("arial.ttf", 48)
        except:
            font = ImageFont.load_default()
    
    # 计算emoji位置（居中）
    bbox = draw.textbbox((0, 0), emoji, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    draw.text((x, y), emoji, font=font, fill=(255, 255, 255, 255))
    
    if filename:
        img.save(filename, 'PNG')
        print(f"已生成: {filename}")
    
    return img

def main():
    """主函数"""
    # 创建输出目录
    output_dir = "button_icons"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    print("正在生成按钮图标...")
    
    # 生成主要按钮图标
    buttons = [
        ("主屋", (139, 110, 99), (255, 255, 255)),    # 棕色
        ("后院", (76, 175, 80), (255, 255, 255)),     # 绿色
        ("商店", (255, 152, 0), (255, 255, 255)),     # 橙色
        ("微信", (76, 175, 80), (255, 255, 255)),     # 绿色
        ("设置", (96, 125, 139), (255, 255, 255)),    # 蓝灰色
        ("成就", (255, 193, 7), (255, 255, 255)),     # 黄色
    ]
    
    for text, bg_color, text_color in buttons:
        filename = os.path.join(output_dir, f"{text}_button.png")
        create_button_icon(text, bg_color, text_color, filename=filename)
    
    # 生成emoji图标
    emoji_icons = [
        ("🏠", (139, 110, 99), "house_icon.png"),     # 房子
        ("🌳", (76, 175, 80), "backyard_icon.png"),   # 树
        ("🛒", (255, 152, 0), "shop_icon.png"),       # 购物车
        ("💬", (76, 175, 80), "wechat_icon.png"),     # 聊天
        ("⚙️", (96, 125, 139), "settings_icon.png"),  # 设置
        ("🏆", (255, 193, 7), "achievement_icon.png"), # 奖杯
        ("🪙", (255, 193, 7), "coin_icon.png"),       # 金币
        ("💎", (33, 150, 243), "gem_icon.png"),       # 宝石
    ]
    
    for emoji, bg_color, filename in emoji_icons:
        filepath = os.path.join(output_dir, filename)
        create_icon_with_emoji(emoji, bg_color, filename=filepath)
    
    print(f"\n所有图标已生成到 {output_dir} 目录")
    print("请将这些图标导入到Cocos Creator项目中使用")

if __name__ == "__main__":
    main()
