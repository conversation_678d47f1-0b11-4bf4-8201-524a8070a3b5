@echo off
chcp 65001
echo ========================================
echo 我的动物收容所 - 项目启动器
echo ========================================
echo.

echo 正在启动Web服务器...
start "Web Server" cmd /k "python -m http.server 8080"

echo 等待服务器启动...
timeout /t 3 /nobreak >nul

echo 正在打开浏览器...
start http://localhost:8080

echo.
echo ========================================
echo 项目已启动！
echo ========================================
echo Web服务器: http://localhost:8080
echo 屏幕尺寸: 720x1080 (竖屏)
echo.
echo 使用说明:
echo 1. Web版本已在浏览器中打开
echo 2. 在Cocos Creator中打开此项目文件夹
echo 3. 主场景文件: assets/MainScene.scene
echo 4. 主脚本文件: assets/scripts/scenes/MainScene.ts
echo.
echo 按任意键退出...
pause >nul
