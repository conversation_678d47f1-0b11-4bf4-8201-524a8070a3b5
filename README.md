# 我的动物收容所 (My Animal Shelter)

一个使用 Cocos Creator 3.8.6 开发的温馨动物收容所管理游戏。

## 🎮 游戏特色

- **温馨的动物收容所主题** - 照顾可爱的小动物
- **竖屏设计** - 专为手机优化的 720x1080 分辨率
- **多场景管理** - 主屋、后院、商店等多个场景
- **经济系统** - 金币和宝石的收集与消费
- **成就系统** - 丰富的游戏成就等待解锁
- **微信小游戏支持** - 可发布到微信小游戏平台

## 🚀 快速开始

### 方法一：一键启动
双击运行 `start_project.bat` 文件，会自动：
- 启动Web服务器
- 打开浏览器查看游戏演示
- 显示项目使用说明

### 方法二：手动启动

#### Web版本预览
```bash
# 启动Web服务器
python -m http.server 8080

# 在浏览器中访问
http://localhost:8080
```

#### Cocos Creator开发
1. 打开 Cocos Creator 3.8.6
2. 选择"打开项目"
3. 选择此项目文件夹
4. 主场景：`assets/MainScene.scene`

## 📁 项目结构

```
myzoo/
├── assets/                     # 游戏资源
│   ├── scripts/               # TypeScript脚本
│   │   ├── scenes/           # 场景脚本
│   │   │   ├── BaseScene.ts  # 基础场景类
│   │   │   ├── MainScene.ts  # 主场景脚本
│   │   │   ├── HouseScene.ts # 主屋场景
│   │   │   └── ...
│   │   ├── managers/         # 管理器
│   │   │   ├── DataManager.ts
│   │   │   ├── EventManager.ts
│   │   │   └── SceneManager.ts
│   │   ├── config/           # 配置文件
│   │   │   └── GameConfig.ts
│   │   └── data/             # 数据结构
│   │       └── GameData.ts
│   ├── textures/             # 图片资源
│   │   └── ui/              # UI图片
│   │       ├── buttons/     # 按钮图标
│   │       └── icons/       # 图标
│   └── MainScene.scene       # 主场景文件
├── settings/                  # 项目设置
├── button_icons/             # 生成的按钮图标
├── index.html               # Web演示页面
├── start_project.bat        # 一键启动脚本
└── README.md               # 项目说明
```

## 🎯 核心功能

### 场景管理
- **MainScene** - 游戏主界面，包含所有主要功能入口
- **HouseScene** - 主屋场景，动物居住和互动
- **BackyardScene** - 后院场景，户外活动区域
- **ShopScene** - 商店场景，购买物品和升级
- **SettingsScene** - 设置场景，游戏配置
- **AchievementScene** - 成就场景，查看游戏进度

### 游戏系统
- **经济系统** - 金币和宝石的获取与消费
- **动物系统** - 动物的收容、照顾和互动
- **时间系统** - 基于真实时间的游戏机制
- **通知系统** - 动物来访和事件提醒
- **数据持久化** - 游戏进度自动保存

## 🛠️ 技术规格

- **引擎版本**: Cocos Creator 3.8.6
- **开发语言**: TypeScript
- **屏幕分辨率**: 720x1080 (竖屏)
- **目标平台**: 
  - 微信小游戏
  - iOS/Android
  - Web浏览器

## 📱 屏幕适配

游戏专为竖屏设计，分辨率为 720x1080：
- 适合手机竖屏操作
- 优化的UI布局
- 响应式设计支持不同屏幕尺寸

## 🎨 UI设计

- **温馨色调** - 以绿色为主的自然色彩
- **圆角设计** - 友好的视觉风格
- **图标系统** - 直观的emoji和图标
- **动画效果** - 流畅的过渡和反馈

## 🔧 开发说明

### 添加新场景
1. 在 `assets/scripts/scenes/` 创建新的场景脚本
2. 继承 `BaseScene` 类
3. 在 `SceneManager.ts` 中注册场景名称
4. 创建对应的 `.scene` 文件

### 修改游戏配置
编辑 `assets/scripts/config/GameConfig.ts` 文件：
- 经济配置 (金币、宝石)
- UI配置 (颜色、尺寸)
- 游戏玩法配置

### 添加新功能
1. 在对应的管理器中添加逻辑
2. 使用 `EventManager` 进行组件间通信
3. 通过 `DataManager` 管理数据持久化

## 🐛 故障排除

### 常见问题

**Q: Cocos Creator中看不到画面？**
A: 确保：
1. 主场景设置正确 (`assets/MainScene.scene`)
2. Canvas节点绑定了MainScene脚本
3. 项目设置中启动场景配置正确

**Q: TypeScript编译错误？**
A: 检查：
1. 导入语句是否正确（不要包含.ts扩展名）
2. 类型定义是否完整
3. Cocos Creator版本是否为3.8.6

**Q: Web版本无法访问？**
A: 确认：
1. Python已安装
2. 端口8080未被占用
3. 防火墙设置允许本地服务器

## 📄 许可证

本项目仅供学习和演示使用。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**享受与可爱动物们的温馨时光！** 🐱🐶🐰
