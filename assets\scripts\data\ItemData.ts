/**
 * 道具数据结构定义
 * 包含道具的基础数据和类型定义
 */

/**
 * 道具数据接口
 */
export interface ItemData {
    id: string;                 // 道具ID
    name: string;               // 道具名称
    type: string;               // 道具类型
    rarity: string;             // 稀有度
    description: string;        // 描述
    iconPath: string;           // 图标路径
    price: number;              // 价格
    effect: string;             // 效果描述
}

/**
 * 道具类型枚举
 */
export enum ItemType {
    FOOD = "food",                      // 食物
    TOY = "toy",                        // 玩具
    TOOL = "tool",                      // 工具
    SUMMON_SCROLL = "summon_scroll",    // 召唤卷轴
    DECORATION = "decoration"           // 装饰
}

/**
 * 道具稀有度枚举
 */
export enum ItemRarity {
    COMMON = "common",          // 普通
    UNCOMMON = "uncommon",      // 不常见
    RARE = "rare",              // 稀有
    EPIC = "epic",              // 史诗
    LEGENDARY = "legendary"     // 传说
}

/**
 * 背包道具数据
 */
export interface InventoryItem {
    itemId: string;             // 道具ID
    count: number;              // 数量
    obtainTime: number;         // 获得时间
    isNew: boolean;             // 是否为新获得
}

/**
 * 道具使用结果
 */
export interface ItemUseResult {
    success: boolean;           // 是否成功使用
    message: string;            // 结果消息
    effects: ItemEffect[];      // 效果列表
    consumeItem: boolean;       // 是否消耗道具
}

/**
 * 道具效果
 */
export interface ItemEffect {
    type: string;               // 效果类型
    value: number;              // 效果数值
    duration?: number;          // 持续时间（秒）
    target?: string;            // 作用目标
}

/**
 * 道具配置
 */
export const ItemConfig = {
    // 食物类道具效果
    FOOD_EFFECTS: {
        HUNGER_RESTORE: 20,         // 饥饿值恢复
        HAPPINESS_BONUS: 5,         // 快乐值奖励
        HEALTH_BONUS: 3,            // 健康值奖励
    },
    
    // 玩具类道具效果
    TOY_EFFECTS: {
        HAPPINESS_RESTORE: 25,      // 快乐值恢复
        ENERGY_COST: 10,            // 体力消耗
        FRIENDSHIP_BONUS: 8,        // 好感度奖励
    },
    
    // 工具类道具效果
    TOOL_EFFECTS: {
        CLEANLINESS_RESTORE: 30,    // 清洁度恢复
        HEALTH_RESTORE: 15,         // 健康值恢复
        EFFICIENCY_BONUS: 1.5,      // 效率加成
    },
    
    // 装饰类道具效果
    DECORATION_EFFECTS: {
        COMFORT_BONUS: 20,          // 舒适度加成
        HAPPINESS_PASSIVE: 2,       // 被动快乐值增长
        ATTRACTION_BONUS: 1.2,      // 吸引力加成
    }
};

/**
 * 道具类型配置
 */
export const ItemTypeConfig = {
    [ItemType.FOOD]: {
        name: "食物",
        description: "用于喂养动物，恢复饥饿值",
        maxStack: 99,
        consumable: true,
        category: "consumable"
    },
    [ItemType.TOY]: {
        name: "玩具",
        description: "用于与动物玩耍，增加快乐值",
        maxStack: 10,
        consumable: false,
        category: "interactive"
    },
    [ItemType.TOOL]: {
        name: "工具",
        description: "用于照料动物，提供各种辅助功能",
        maxStack: 5,
        consumable: false,
        category: "utility"
    },
    [ItemType.SUMMON_SCROLL]: {
        name: "召唤卷轴",
        description: "用于召唤新的动物朋友",
        maxStack: 20,
        consumable: true,
        category: "special"
    },
    [ItemType.DECORATION]: {
        name: "装饰",
        description: "用于装饰房屋，提供环境加成",
        maxStack: 1,
        consumable: false,
        category: "decoration"
    }
};

/**
 * 稀有度配置
 */
export const RarityConfig = {
    [ItemRarity.COMMON]: {
        name: "普通",
        color: "#FFFFFF",
        dropRate: 0.6,
        priceMultiplier: 1.0
    },
    [ItemRarity.UNCOMMON]: {
        name: "不常见",
        color: "#00FF00",
        dropRate: 0.25,
        priceMultiplier: 2.0
    },
    [ItemRarity.RARE]: {
        name: "稀有",
        color: "#0080FF",
        dropRate: 0.1,
        priceMultiplier: 5.0
    },
    [ItemRarity.EPIC]: {
        name: "史诗",
        color: "#8000FF",
        dropRate: 0.04,
        priceMultiplier: 10.0
    },
    [ItemRarity.LEGENDARY]: {
        name: "传说",
        color: "#FF8000",
        dropRate: 0.01,
        priceMultiplier: 25.0
    }
};

/**
 * 道具工具类
 */
export class ItemUtils {
    /**
     * 根据稀有度获取颜色
     */
    public static getRarityColor(rarity: ItemRarity): string {
        return RarityConfig[rarity]?.color || "#FFFFFF";
    }
    
    /**
     * 根据稀有度获取价格倍数
     */
    public static getRarityPriceMultiplier(rarity: ItemRarity): number {
        return RarityConfig[rarity]?.priceMultiplier || 1.0;
    }
    
    /**
     * 检查道具是否可堆叠
     */
    public static isStackable(itemType: ItemType): boolean {
        const config = ItemTypeConfig[itemType];
        return config ? config.maxStack > 1 : false;
    }
    
    /**
     * 获取道具最大堆叠数量
     */
    public static getMaxStack(itemType: ItemType): number {
        return ItemTypeConfig[itemType]?.maxStack || 1;
    }
    
    /**
     * 检查道具是否为消耗品
     */
    public static isConsumable(itemType: ItemType): boolean {
        return ItemTypeConfig[itemType]?.consumable || false;
    }
    
    /**
     * 格式化道具数量显示
     */
    public static formatItemCount(count: number): string {
        if (count >= 1000000) {
            return `${(count / 1000000).toFixed(1)}M`;
        } else if (count >= 1000) {
            return `${(count / 1000).toFixed(1)}K`;
        }
        return count.toString();
    }
    
    /**
     * 生成道具使用效果描述
     */
    public static generateEffectDescription(effects: ItemEffect[]): string {
        const descriptions = effects.map(effect => {
            switch (effect.type) {
                case "hunger":
                    return `饥饿值 +${effect.value}`;
                case "happiness":
                    return `快乐值 +${effect.value}`;
                case "health":
                    return `健康值 +${effect.value}`;
                case "friendship":
                    return `好感度 +${effect.value}`;
                case "cleanliness":
                    return `清洁度 +${effect.value}`;
                default:
                    return `${effect.type} +${effect.value}`;
            }
        });
        
        return descriptions.join(", ");
    }
}
