<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>我的动物收容所</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        
        .container {
            width: 720px;
            height: 1080px;
            max-width: 100vw;
            max-height: 100vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
            position: relative;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .title {
            font-size: 32px;
            font-weight: bold;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 16px;
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-around;
            padding: 15px;
            background: rgba(255,255,255,0.9);
            margin: 20px;
            border-radius: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .status-item {
            display: flex;
            align-items: center;
            font-weight: bold;
            color: #2E7D32;
        }
        
        .status-icon {
            width: 24px;
            height: 24px;
            margin-right: 8px;
            border-radius: 50%;
        }
        
        .coin-icon {
            background: radial-gradient(circle, #FFD700 0%, #FFA000 100%);
            border: 2px solid #FF8F00;
        }
        
        .gem-icon {
            background: linear-gradient(45deg, #2196F3 0%, #1976D2 100%);
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
            margin: 20px;
        }
        
        .game-button {
            background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
            border: none;
            border-radius: 15px;
            padding: 20px;
            color: white;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            min-height: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .game-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.3);
        }
        
        .game-button:active {
            transform: translateY(0);
        }
        
        .house-button {
            background: linear-gradient(135deg, #8D6E63 0%, #6D4C41 100%);
        }
        
        .shop-button {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
        }
        
        .settings-button {
            background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
        }
        
        .achievement-button {
            background: linear-gradient(135deg, #FFC107 0%, #FF8F00 100%);
        }
        
        .welcome-text {
            text-align: center;
            padding: 20px;
            font-size: 18px;
            color: #4CAF50;
            font-style: italic;
        }
        
        .notification {
            background: linear-gradient(135deg, #FFEB3B 0%, #FFC107 100%);
            border: 3px solid #FF8F00;
            border-radius: 15px;
            padding: 15px;
            margin: 20px;
            text-align: center;
            font-weight: bold;
            color: #E65100;
            animation: bounce 2s infinite;
            cursor: pointer;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-5px);
            }
            60% {
                transform: translateY(-2px);
            }
        }
        
        .footer {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .container {
                width: 100vw;
                height: 100vh;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">我的动物收容所</h1>
            <p class="subtitle">欢迎来到温馨的动物之家</p>
        </div>
        
        <div class="status-bar">
            <div class="status-item">
                <div class="status-icon coin-icon"></div>
                <span id="coins">1000</span>
            </div>
            <div class="status-item">
                <div class="status-icon gem-icon"></div>
                <span id="gems">50</span>
            </div>
        </div>
        
        <div class="welcome-text" id="welcomeText">
            欢迎来到我的动物收容所！
        </div>
        
        <div class="notification" id="animalNotification" style="display: none;">
            🐱 有可爱的小动物来访问了！点击查看
        </div>
        
        <div class="button-grid">
            <button class="game-button house-button" onclick="goToScene('house')">
                🏠<br>主屋
            </button>
            <button class="game-button" onclick="goToScene('backyard')">
                🌳<br>后院
            </button>
            <button class="game-button shop-button" onclick="goToScene('shop')">
                🛒<br>商店
            </button>
            <button class="game-button" onclick="goToScene('wechat')">
                💬<br>微信
            </button>
            <button class="game-button settings-button" onclick="goToScene('settings')">
                ⚙️<br>设置
            </button>
            <button class="game-button achievement-button" onclick="goToScene('achievement')">
                🏆<br>成就
            </button>
        </div>
        
        <div class="footer">
            <p>Cocos Creator 3.8.6 | 720x1080</p>
            <p>动物收容所游戏演示</p>
        </div>
    </div>

    <script>
        // 游戏数据
        let gameData = {
            coins: 1000,
            gems: 50,
            playerName: "新玩家"
        };
        
        // 更新欢迎文本
        function updateWelcomeText() {
            const hour = new Date().getHours();
            let greeting = "";
            
            if (hour < 6) {
                greeting = "夜深了，动物们都在休息";
            } else if (hour < 12) {
                greeting = "早上好！新的一天开始了";
            } else if (hour < 18) {
                greeting = "下午好！动物们很活跃";
            } else {
                greeting = "晚上好！准备休息了吗";
            }
            
            document.getElementById('welcomeText').textContent = greeting;
        }
        
        // 场景跳转
        function goToScene(sceneName) {
            alert(`正在进入${sceneName}场景...\n(这是演示版本，实际游戏中会切换到对应场景)`);
        }
        
        // 动物来访
        function showAnimalVisit() {
            const notification = document.getElementById('animalNotification');
            notification.style.display = 'block';
            
            setTimeout(() => {
                notification.style.display = 'none';
            }, 5000);
        }
        
        // 点击动物通知
        document.getElementById('animalNotification').onclick = function() {
            const animals = ['小猫咪', '小狗狗', '小兔子', '小松鼠', '小鸟儿'];
            const randomAnimal = animals[Math.floor(Math.random() * animals.length)];
            
            alert(`一只可爱的${randomAnimal}来访问了！\n获得了10金币！`);
            
            gameData.coins += 10;
            document.getElementById('coins').textContent = gameData.coins;
            this.style.display = 'none';
        };
        
        // 初始化
        updateWelcomeText();
        
        // 定时更新
        setInterval(updateWelcomeText, 60000); // 每分钟更新一次
        
        // 随机动物来访
        setInterval(() => {
            if (Math.random() < 0.3) { // 30%概率
                showAnimalVisit();
            }
        }, 10000); // 每10秒检查一次
        
        console.log("我的动物收容所 - Web演示版本");
        console.log("屏幕尺寸: 720x1080");
        console.log("Cocos Creator 3.8.6");
    </script>
</body>
</html>
