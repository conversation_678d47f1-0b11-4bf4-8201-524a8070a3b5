import { _decorator, Component, Node, Button, Label } from 'cc';
import { BaseScene } from './BaseScene';
import { SceneNames } from '../managers/SceneManager';
import { GameConfig } from '../config/GameConfig';
import { UIManager } from '../managers/UIManager';
const { ccclass, property } = _decorator;

/**
 * 主场景控制器
 * 游戏的主入口场景，包含主要的导航功能
 */
@ccclass('MainScene')
export class MainScene extends BaseScene {

    @property({ type: Button, tooltip: "进入主屋按钮" })
    public houseButton: Button = null;

    @property({ type: Button, tooltip: "进入后院按钮" })
    public backyardButton: Button = null;

    @property({ type: Button, tooltip: "打开商店按钮" })
    public shopButton: Button = null;

    @property({ type: Button, tooltip: "打开微信按钮" })
    public wechatButton: Button = null;

    @property({ type: Button, tooltip: "设置按钮" })
    public settingsButton: Button = null;

    @property({ type: Button, tooltip: "成就按钮" })
    public achievementButton: Button = null;

    @property({ type: Label, tooltip: "玩家名称标签" })
    public playerNameLabel: Label = null;

    @property({ type: Label, tooltip: "金币数量标签" })
    public coinsLabel: Label = null;

    @property({ type: Label, tooltip: "宝石数量标签" })
    public gemsLabel: Label = null;

    @property({ type: Label, tooltip: "欢迎文本" })
    public welcomeLabel: Label = null;

    @property({ type: Node, tooltip: "动物来访提示" })
    public animalVisitNotification: Node = null;

    @property({ type: Node, tooltip: "状态栏容器" })
    public statusBar: Node = null;

    @property({ type: Node, tooltip: "按钮容器" })
    public buttonContainer: Node = null;

    // 私有变量
    private _animalVisitTimer: number = 0;
    private _uiUpdateTimer: number = 0;

    onLoad() {
        super.onLoad();
        this.sceneName = "MainScene";
        this.showBackButton = false; // 主场景不显示返回按钮
        this.setupButtons();
        this.setupUI();
        this.startTimers();
    }

    protected onSceneStart() {
        super.onSceneStart();
        this.updateUI();
        this.checkAnimalVisit();
        this.playBackgroundMusic();
        this.checkDailyReward();
        this.showWelcomeMessage();
    }

    start() {
        // 场景启动后的初始化
        this.playEntranceAnimation();
    }

    /**
     * 设置按钮事件
     */
    private setupButtons() {
        if (this.houseButton) {
            this.houseButton.node.on(Button.EventType.CLICK, this.onHouseButtonClick, this);
        }

        if (this.backyardButton) {
            this.backyardButton.node.on(Button.EventType.CLICK, this.onBackyardButtonClick, this);
        }

        if (this.shopButton) {
            this.shopButton.node.on(Button.EventType.CLICK, this.onShopButtonClick, this);
        }

        if (this.wechatButton) {
            this.wechatButton.node.on(Button.EventType.CLICK, this.onWeChatButtonClick, this);
        }

        if (this.settingsButton) {
            this.settingsButton.node.on(Button.EventType.CLICK, this.onSettingsButtonClick, this);
        }

        if (this.achievementButton) {
            this.achievementButton.node.on(Button.EventType.CLICK, this.onAchievementButtonClick, this);
        }
    }

    /**
     * 设置UI初始状态
     */
    private setupUI() {
        // 设置欢迎文本
        if (this.welcomeLabel) {
            this.welcomeLabel.string = "欢迎来到我的动物收容所！";
        }

        // 初始隐藏动物来访通知
        if (this.animalVisitNotification) {
            this.animalVisitNotification.active = false;
        }

        // 设置按钮初始状态
        this.setButtonsInteractable(true);
    }

    /**
     * 启动定时器
     */
    private startTimers() {
        // 每5秒检查一次动物来访
        this._animalVisitTimer = setInterval(() => {
            this.checkAnimalVisit();
        }, 5000);

        // 每秒更新UI
        this._uiUpdateTimer = setInterval(() => {
            this.updateUI();
        }, 1000);
    }

    /**
     * 设置按钮可交互状态
     */
    private setButtonsInteractable(interactable: boolean) {
        const buttons = [this.houseButton, this.backyardButton, this.shopButton, this.wechatButton];
        buttons.forEach(button => {
            if (button) {
                button.interactable = interactable;
            }
        });
    }

    /**
     * 更新UI显示
     */
    private updateUI() {
        const playerData = this.getPlayerData();

        if (this.playerNameLabel) {
            this.playerNameLabel.string = playerData.playerName || "新玩家";
        }

        if (this.coinsLabel) {
            this.coinsLabel.string = this.formatNumber(playerData.coins || 0);
        }

        if (this.gemsLabel) {
            this.gemsLabel.string = this.formatNumber(playerData.gems || 0);
        }

        // 更新欢迎文本（根据时间）
        this.updateWelcomeText();
    }

    /**
     * 格式化数字显示
     */
    private formatNumber(num: number): string {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + "M";
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + "K";
        }
        return num.toString();
    }

    /**
     * 更新欢迎文本
     */
    private updateWelcomeText() {
        if (!this.welcomeLabel) return;

        const hour = new Date().getHours();
        let greeting = "";

        if (hour < 6) {
            greeting = "夜深了，动物们都在休息";
        } else if (hour < 12) {
            greeting = "早上好！新的一天开始了";
        } else if (hour < 18) {
            greeting = "下午好！动物们很活跃";
        } else {
            greeting = "晚上好！准备休息了吗";
        }

        this.welcomeLabel.string = greeting;
    }

    /**
     * 显示欢迎消息
     */
    private showWelcomeMessage() {
        const playerData = this.getPlayerData();
        const isFirstTime = !playerData.lastLoginTime;

        if (isFirstTime) {
            UIManager.instance.showMessage("欢迎来到动物收容所！\n这里有很多可爱的动物等着你！");
        } else {
            UIManager.instance.showToast("欢迎回来！");
        }
    }

    /**
     * 播放入场动画
     */
    private playEntranceAnimation() {
        // 这里可以添加按钮的入场动画
        console.log("播放主场景入场动画");

        // 简单的按钮动画示例
        if (this.buttonContainer) {
            this.buttonContainer.setScale(0.8, 0.8, 1);
            // 这里可以使用Tween动画让按钮放大到正常大小
        }
    }

    /**
     * 检查动物来访
     */
    private checkAnimalVisit() {
        // 这里实现动物来访的逻辑
        // 根据时间和概率决定是否有动物来访
        const shouldShowVisit = this.shouldAnimalVisit();
        
        if (this.animalVisitNotification) {
            this.animalVisitNotification.active = shouldShowVisit;
        }
    }

    /**
     * 判断是否应该有动物来访
     */
    private shouldAnimalVisit(): boolean {
        // 简单的随机逻辑，后续可以根据时间、道具等因素优化
        return Math.random() < 0.3; // 30%的概率
    }

    /**
     * 播放背景音乐
     */
    private playBackgroundMusic() {
        // 这里实现背景音乐播放逻辑
        console.log("播放主场景背景音乐");
    }

    /**
     * 检查每日登录奖励
     */
    private checkDailyReward() {
        const playerData = this.getPlayerData();
        const now = Date.now();
        const lastLogin = playerData.lastLoginTime;

        // 检查是否是新的一天
        const isNewDay = !this.isSameDay(new Date(lastLogin), new Date(now));

        if (isNewDay) {
            this.showDailyReward();
            playerData.lastLoginTime = now;
            this.saveSceneData();
        }
    }

    /**
     * 判断是否是同一天
     */
    private isSameDay(date1: Date, date2: Date): boolean {
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate();
    }

    /**
     * 显示每日登录奖励
     */
    private showDailyReward() {
        this.showSuccess("获得每日登录奖励！");

        // 添加奖励
        this.addCoins(GameConfig.ECONOMY_CONFIG.DAILY_LOGIN_REWARD);
        this.addGems(GameConfig.ECONOMY_CONFIG.AD_REWARD_GEMS);

        // 更新UI
        this.updateUI();
    }

    /**
     * 主屋按钮点击事件
     */
    private onHouseButtonClick() {
        console.log("进入主屋");
        this.gotoScene(SceneNames.HOUSE);
    }

    /**
     * 后院按钮点击事件
     */
    private onBackyardButtonClick() {
        console.log("进入后院");
        this.gotoScene(SceneNames.BACKYARD);
    }

    /**
     * 商店按钮点击事件
     */
    private onShopButtonClick() {
        console.log("打开商店");
        this.gotoScene(SceneNames.SHOP);
    }

    /**
     * 微信按钮点击事件
     */
    private onWeChatButtonClick() {
        console.log("打开微信");
        this.gotoScene(SceneNames.WECHAT);
    }

    /**
     * 设置按钮点击事件
     */
    private onSettingsButtonClick() {
        console.log("打开设置");
        UIManager.instance.showMessage("设置功能开发中...");
    }

    /**
     * 成就按钮点击事件
     */
    private onAchievementButtonClick() {
        console.log("打开成就");
        UIManager.instance.showMessage("成就系统开发中...");
    }

    /**
     * 动物来访通知点击事件
     */
    public onAnimalVisitClick() {
        console.log("有动物来访！");

        // 隐藏通知
        if (this.animalVisitNotification) {
            this.animalVisitNotification.active = false;
        }

        // 显示动物来访界面
        this.showAnimalVisitDialog();
    }

    /**
     * 显示动物来访对话框
     */
    private showAnimalVisitDialog() {
        const animalNames = ["小猫咪", "小狗狗", "小兔子", "小松鼠", "小鸟儿"];
        const randomAnimal = animalNames[Math.floor(Math.random() * animalNames.length)];

        UIManager.instance.showMessage(`一只可爱的${randomAnimal}来访问了！\n它看起来很友好，要和它互动吗？`);

        // 给予奖励
        this.addCoins(10);
        this.updateUI();
        UIManager.instance.showToast("获得了10金币！");
    }

    onDestroy() {
        // 清理定时器
        if (this._animalVisitTimer) {
            clearInterval(this._animalVisitTimer);
            this._animalVisitTimer = 0;
        }

        if (this._uiUpdateTimer) {
            clearInterval(this._uiUpdateTimer);
            this._uiUpdateTimer = 0;
        }

        // 清理事件监听
        if (this.houseButton) {
            this.houseButton.node.off(Button.EventType.CLICK, this.onHouseButtonClick, this);
        }
        if (this.backyardButton) {
            this.backyardButton.node.off(Button.EventType.CLICK, this.onBackyardButtonClick, this);
        }
        if (this.shopButton) {
            this.shopButton.node.off(Button.EventType.CLICK, this.onShopButtonClick, this);
        }
        if (this.wechatButton) {
            this.wechatButton.node.off(Button.EventType.CLICK, this.onWeChatButtonClick, this);
        }
        if (this.settingsButton) {
            this.settingsButton.node.off(Button.EventType.CLICK, this.onSettingsButtonClick, this);
        }
        if (this.achievementButton) {
            this.achievementButton.node.off(Button.EventType.CLICK, this.onAchievementButtonClick, this);
        }
    }
}
